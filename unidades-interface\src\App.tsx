import { useState, useEffect } from 'react'
import './App.css'

interface Unidade {
  id: number
  nomeUsual: string
  nomeCompleto: string
  nomeOficial: string
  sigla: string
  emailUnidade: string
  situacao: string
  tipoUnidade: string
  codDomus: number
  podeLotar: boolean
  statusUnidadeUO: string
}

interface ApiResponse {
  output: {
    entity: Unidade[]
  }
}

function App() {
  const [unidades, setUnidades] = useState<Unidade[]>([])
  const [filteredUnidades, setFilteredUnidades] = useState<Unidade[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [situacaoFilter, setSituacaoFilter] = useState('TODOS')

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/data.json')
        const data: ApiResponse = await response.json()

        // Filtrar apenas unidades extrajudiciais
        const unidadesExtrajudiciais = data.output.entity.filter(
          unidade => unidade.tipoUnidade === 'EXTRAJUDICIAL'
        )

        setUnidades(unidadesExtrajudiciais)
        setFilteredUnidades(unidadesExtrajudiciais)
        setLoading(false)
      } catch (error) {
        console.error('Erro ao carregar dados:', error)
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  useEffect(() => {
    let filtered = unidades

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(unidade =>
        unidade.nomeUsual?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        unidade.nomeCompleto?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        unidade.sigla?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filtrar por situação
    if (situacaoFilter !== 'TODOS') {
      filtered = filtered.filter(unidade => unidade.situacao === situacaoFilter)
    }

    setFilteredUnidades(filtered)
  }, [searchTerm, situacaoFilter, unidades])

  if (loading) {
    return (
      <div className="loading">
        <h2>Carregando dados...</h2>
      </div>
    )
  }

  return (
    <div className="app">
      <header className="header">
        <h1>Unidades Extrajudiciais</h1>
        <p>Total de unidades: {filteredUnidades.length}</p>
      </header>

      <div className="filters">
        <div className="search-container">
          <input
            type="text"
            placeholder="Buscar por nome, sigla..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-container">
          <select
            value={situacaoFilter}
            onChange={(e) => setSituacaoFilter(e.target.value)}
            className="filter-select"
          >
            <option value="TODOS">Todas as situações</option>
            <option value="INSTALADO">Instalado</option>
            <option value="EXTINTO">Extinto</option>
          </select>
        </div>
      </div>

      <div className="unidades-grid">
        {filteredUnidades.map((unidade) => (
          <div key={unidade.id} className="unidade-card">
            <div className="unidade-header">
              <h3>{unidade.nomeUsual || unidade.nomeOficial}</h3>
              <span className={`status ${unidade.situacao?.toLowerCase()}`}>
                {unidade.situacao}
              </span>
            </div>

            <div className="unidade-details">
              <p><strong>Sigla:</strong> {unidade.sigla || 'N/A'}</p>
              <p><strong>Nome Completo:</strong> {unidade.nomeCompleto}</p>
              <p><strong>Email:</strong> {unidade.emailUnidade || 'Não informado'}</p>
              <p><strong>ID:</strong> {unidade.id}</p>
              {unidade.codDomus && (
                <p><strong>Código Domus:</strong> {unidade.codDomus}</p>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredUnidades.length === 0 && (
        <div className="no-results">
          <p>Nenhuma unidade encontrada com os filtros aplicados.</p>
        </div>
      )}
    </div>
  )
}

export default App
